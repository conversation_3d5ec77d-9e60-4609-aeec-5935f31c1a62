import 'package:bus/bloc/current_trip_cubit/current_trip_cubit.dart';
import 'package:bus/bloc/current_trip_cubit/current_trip_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/views/screens/evening_trip_screen/evening_trip_screen.dart';
import 'package:bus/views/screens/morning_trip_screen/morning_trip_screen.dart';
import 'package:bus/views/screens/show_bus_on_map_screen/show_bus_on_map_screen.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class OpenTripScreen extends StatefulWidget {
  static const String routeName = PathRouteName.openTripScreen;

  const OpenTripScreen({super.key});

  @override
  State<OpenTripScreen> createState() => _OpenTripScreenState();
}

class _OpenTripScreenState extends State<OpenTripScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.openTrips.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? _buildBackArrow(AppAssets.arrowBack, context)
            : _buildBackArrow(AppAssets.forwardArrow, context),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Search Bar
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
              child: Container(
                decoration: BoxDecoration(
                  color: TColor.fillFormField,
                  borderRadius: BorderRadius.circular(30.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(13), // 0.05 opacity
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: AppStrings.searchDots.tr(),
                    prefixIcon:
                        const Icon(Icons.search, color: TColor.mainColor),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 15.h),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value.toLowerCase();
                    });
                  },
                ),
              ),
            ),

            // Tab Bar
            Container(
              margin: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(30.r),
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: Colors.white,
                unselectedLabelColor: TColor.dialogName,
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: Colors.transparent,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.r),
                  color: TColor.mainColor,
                ),
                labelStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
                tabs: [
                  Tab(text: AppStrings.allCaps.tr()),
                  Tab(text: AppStrings.supervisorSingle.tr()),
                  Tab(text: AppStrings.driverSingle.tr()),
                ],
                padding: EdgeInsets.all(4.w),
              ),
            ),

            // Tab Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // All Trips Tab
                  _buildTripsTab(context, filterType: null),

                  // Supervisor Trips Tab
                  _buildTripsTab(context, filterType: 'supervisor'),

                  // Driver Trips Tab
                  _buildTripsTab(context, filterType: 'driver'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackArrow(String assetPath, BuildContext context) {
    return InkWell(
      onTap: () => Navigator.pop(context),
      child: SvgPicture.asset(
        assetPath,
        colorFilter: const ColorFilter.mode(TColor.white, BlendMode.srcIn),
        width: 25.w,
        height: 25.w,
      ),
    );
  }

  Widget _buildTripsTab(BuildContext context, {String? filterType}) {
    return BlocBuilder<CurrentTripCubit, CurrentTripStates>(
      builder: (context, states) {
        if (states is CurrentTripLoadingStates) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  color: TColor.mainColor,
                ),
                SizedBox(height: 20.h),
                CustomText(
                  text: AppStrings.loading.tr(),
                  fontSize: 16,
                  color: TColor.mainColor,
                ),
              ],
            ),
          );
        } else if (states is CurrentTripSuccessStates) {
          return _buildFilteredTripList(context, states, filterType);
        } else {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 50.w,
                  color: TColor.redAccent,
                ),
                SizedBox(height: 20.h),
                CustomText(
                  text: tr('Error occurred'),
                  fontSize: 16,
                  color: TColor.redAccent,
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildFilteredTripList(BuildContext context,
      CurrentTripSuccessStates states, String? filterType) {
    // Filter trips based on tab and search query
    final filteredTrips = states.currentTripModels!.data!.where((trip) {
      final bus = trip.bus;
      if (bus == null) return false;

      final admin = bus.admin;
      final driver = bus.driver;

      // Filter by type (supervisor/driver)
      bool matchesType = true;
      if (filterType == 'supervisor') {
        // Show trips that have a supervisor (admin)
        matchesType = admin != null;
      } else if (filterType == 'driver') {
        // Show trips that have a driver
        matchesType = driver != null;
      }
      // If filterType is null or 'all', show all trips that have either admin or driver or both

      // Filter by search query
      bool matchesSearch = true;
      if (_searchQuery.isNotEmpty) {
        final busName = bus.name?.toLowerCase() ?? '';
        final busNumber = bus.car_number?.toLowerCase() ?? '';
        final adminName = admin?.name?.toLowerCase() ?? '';
        final driverName = driver?.name?.toLowerCase() ?? '';

        matchesSearch = busName.contains(_searchQuery) ||
            busNumber.contains(_searchQuery) ||
            adminName.contains(_searchQuery) ||
            driverName.contains(_searchQuery);
      }

      return matchesType && matchesSearch;
    }).toList();

    if (filteredTrips.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.directions_bus_outlined,
              size: 70.w,
              color: TColor.grey5,
            ),
            SizedBox(height: 20.h),
            CustomText(
              text: AppStrings.noTripsAvailable.tr(),
              fontSize: 18,
              color: TColor.grey5,
              fontW: FontWeight.w500,
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      itemCount: filteredTrips.length,
      padding: EdgeInsets.symmetric(vertical: 20.w, horizontal: 20.w),
      itemBuilder: (context, index) {
        final tripData = filteredTrips[index];
        return buildTripItem(context, states, tripData);
      },
      separatorBuilder: (context, index) => SizedBox(height: 15.h),
    );
  }

  Widget buildTripItem(
    BuildContext context,
    CurrentTripSuccessStates states,
    dynamic tripData,
  ) {
    final bus = tripData.bus;
    if (bus == null) return const SizedBox();

    final admin = bus.admin;
    final driver = bus.driver;

    // If both admin and driver are null, return empty widget
    if (admin == null && driver == null) return const SizedBox();

    // Determine if we should show a combined card or separate cards
    final bool showCombined = admin != null && driver != null;

    if (showCombined) {
      // Show a combined card with tabs for admin and driver
      return Container(
        margin: EdgeInsets.symmetric(horizontal: 20.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(20),
              blurRadius: 10,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          children: [
            // Tab header
            Container(
              decoration: BoxDecoration(
                color: TColor.mainColor.withAlpha(20),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(15.r),
                  topRight: Radius.circular(15.r),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        // Could implement tab switching if needed
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        decoration: BoxDecoration(
                          color: TColor.mainColor,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(15.r),
                            topRight: Radius.circular(15.r),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            AppStrings.supervisorAndDriver.tr(),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16.sp,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Admin info
            _buildPersonCard(
              context: context,
              imagePath: admin!.logo_path,
              name: admin.name ?? AppStrings.notFoundCaps.tr(),
              busName: bus.name ?? AppStrings.notFoundCaps.tr(),
              busNumber: bus.car_number ?? AppStrings.notFoundCaps.tr(),
              type: admin.type ?? '',
              tripData: tripData,
              bus: bus,
              isAdmin: true,
            ),

            // Driver info
            _buildPersonCard(
              context: context,
              imagePath: driver?.logo_path,
              name: driver.name ?? AppStrings.notFoundCaps.tr(),
              busName: bus.name ?? AppStrings.notFoundCaps.tr(),
              busNumber: bus.car_number ?? AppStrings.notFoundCaps.tr(),
              type: driver.type ?? '',
              tripData: tripData,
              bus: bus,
              isAdmin: false,
            ),
          ],
        ),
      );
    } else {
      // Show individual card for either admin or driver
      // Determine which person to show based on availability
      final personToShow = admin ?? driver;
      final isAdmin = admin != null;

      return Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: _buildPersonCard(
          context: context,
          imagePath: personToShow?.logo_path,
          name: personToShow?.name ?? AppStrings.notFoundCaps.tr(),
          busName: bus.name ?? AppStrings.notFoundCaps.tr(),
          busNumber: bus.car_number ?? AppStrings.notFoundCaps.tr(),
          type: personToShow?.type ?? '',
          tripData: tripData,
          bus: bus,
          isAdmin: isAdmin,
        ),
      );
    }
  }

  Widget _buildPersonCard({
    required BuildContext context,
     String? imagePath,
    required String name,
    required String busName,
    required String busNumber,
    required String type,
    required dynamic tripData,
    required dynamic bus,
    bool isAdmin = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            TColor.fillFormField,
          ],
        ),
        borderRadius: BorderRadius.circular(15.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(20),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(15.w),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left side with image and info
                Expanded(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Hero(
                        tag: 'trip_image_${tripData.id}',
                        child: Container(
                          height: 80.w,
                          width: 80.w,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.r),
                            border: Border.all(
                              color: TColor.mainColor.withAlpha(100),
                              width: 2,
                            ),
                            image: DecorationImage(
                              image: imagePath != null
                                  ? NetworkImage(imagePath)
                                  : const AssetImage(
                                          "assets/images/all_driver_icon.svg")
                                      as ImageProvider,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 15.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            buildInfoRow(
                              AppStrings.name.tr(),
                              name,
                            ),
                            SizedBox(height: 5.h),
                            buildInfoRow(
                              AppStrings.bus_name.tr(),
                              busName,
                            ),
                            SizedBox(height: 5.h),
                            buildInfoRow(
                              AppStrings.busNumber.tr(),
                              busNumber,
                            ),
                            SizedBox(height: 5.h),
                            buildInfoRow(
                              AppStrings.tripTypeCaps.tr(),
                              tripData.trip_type == 'start_day'
                                  ? AppStrings.morningTrip.tr()
                                  : AppStrings.eveningTrip.tr(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Buttons row at the bottom
            Padding(
              padding: EdgeInsets.only(top: 15.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Track button
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ShowBusOnMapScreen(
                              name: name,
                              busName: bus.name ?? '',
                              userableId: tripData.userable_id.toString(),
                              busId: bus.id.toString(),
                              type: type,
                              tripId: tripData.id.toString(),
                            ),
                          ),
                        );
                      },
                      icon: const Icon(Icons.location_on),
                      label: Text(AppStrings.track.tr()),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: TColor.mainColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.r),
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical: 12.h,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 10.w),

                  // Info button
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Navigate to different screens based on trip_type
                        final isMorningTrip = tripData.trip_type == 'start_day';

                        if (isMorningTrip) {
                          // Navigate to Morning Trip Screen
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MorningTripScreen(
                                name: name,
                                busName: bus.name ?? '',
                                userableId: tripData.userable_id.toString(),
                                busId: bus.id.toString(),
                                type: type,
                                tripData: tripData,
                              ),
                            ),
                          );
                        } else {
                          // Navigate to Evening Trip Screen
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => EveningTripScreen(
                                name: name,
                                busName: bus.name ?? '',
                                userableId: tripData.userable_id.toString(),
                                busId: bus.id.toString(),
                                type: type,
                                tripData: tripData,
                              ),
                            ),
                          );
                        }
                      },
                      icon: const Icon(Icons.info_outline),
                      label: Text(AppStrings.info.tr()),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: TColor.mainColor,
                        elevation: 0,
                        side: const BorderSide(color: TColor.mainColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30.r),
                        ),
                        padding: EdgeInsets.symmetric(
                          vertical: 12.h,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label: ',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: TColor.namePersonal,
            fontSize: 14.sp,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: TColor.borderAvatar,
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}
